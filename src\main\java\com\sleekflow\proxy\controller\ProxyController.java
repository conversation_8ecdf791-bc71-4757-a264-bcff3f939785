package com.sleekflow.proxy.controller;

import com.sleekflow.proxy.service.HttpForwardingService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.util.Collections;
import java.util.Enumeration;

@RestController
public class ProxyController {

    private static final Logger logger = LoggerFactory.getLogger(ProxyController.class);

    private final HttpForwardingService httpForwardingService;

    public ProxyController(HttpForwardingService httpForwardingService) {
        this.httpForwardingService = httpForwardingService;
    }

    @RequestMapping("/**")
    public Mono<ResponseEntity<byte[]>> proxyRequest(HttpServletRequest request) {
        try {
            HttpMethod method = HttpMethod.valueOf(request.getMethod());
            String pathWithQuery = extractPathWithQuery(request);
            HttpHeaders headers = extractHeaders(request);
            byte[] body = extractBody(request);

            logger.info("Received {} request for path: {}", method, pathWithQuery);
            logger.debug("Request headers: {}", headers);

            if (body != null && body.length > 0) {
                logger.debug("Request body size: {} bytes", body.length);
            }

            return httpForwardingService.forwardRequest(method, pathWithQuery, headers, body);

        } catch (Exception e) {
            logger.error("Error processing proxy request: {}", e.getMessage(), e);

            return Mono.just(ResponseEntity
                    .status(500)
                    .body(("Proxy Error: " + e.getMessage()).getBytes()));
        }
    }

    private String extractPathWithQuery(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String queryString = request.getQueryString();

        if (queryString != null && !queryString.isEmpty()) {
            return requestURI + "?" + queryString;
        }

        return requestURI;
    }

    private HttpHeaders extractHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();

        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                Enumeration<String> headerValues = request.getHeaders(headerName);

                if (headerValues != null) {
                    headers.put(headerName, Collections.list(headerValues));
                }
            }
        }

        return headers;
    }

    private byte[] extractBody(HttpServletRequest request) throws IOException {
        String method = request.getMethod();
        if ("GET".equals(method) || "DELETE".equals(method) || "HEAD".equals(method)) {
            return null;
        }

        int contentLength = request.getContentLength();
        if (contentLength <= 0) {
            return null;
        }

        try {
            return StreamUtils.copyToByteArray(request.getInputStream());
        } catch (IOException e) {
            logger.warn("Error reading request body: {}", e.getMessage());
            return null;
        }
    }
}
