package com.sleekflow.proxy.config;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@ConfigurationProperties(prefix = "proxy")
@Validated
public class ProxyProperties {

    @NotBlank(message = "Destination host cannot be blank")
    private String destinationHost;

    @NotNull(message = "Server port cannot be null")
    @Positive(message = "Server port must be positive")
    private Integer serverPort = 8080;

    private Boolean followRedirects = true;

    public String getDestinationHost() {
        return destinationHost;
    }

    public void setDestinationHost(String destinationHost) {
        this.destinationHost = destinationHost;
    }

    public Integer getServerPort() {
        return serverPort;
    }

    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }

    public Boolean getFollowRedirects() {
        return followRedirects;
    }

    public void setFollowRedirects(Boolean followRedirects) {
        this.followRedirects = followRedirects;
    }

    @Override
    public String toString() {
        return "ProxyProperties{" +
                "destinationHost='" + destinationHost + '\'' +
                ", serverPort=" + serverPort +
                ", followRedirects=" + followRedirects +
                '}';
    }
}
