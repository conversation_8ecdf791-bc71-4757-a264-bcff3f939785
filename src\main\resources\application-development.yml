proxy:
  destinationHost: ${PROXY_DESTINATION_HOST:https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net}
  serverPort: ${PROXY_SERVER_PORT:8080}

server:
  port: ${proxy.serverPort}

logging:
  level:
    com.sleekflow.proxy: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
    org.springframework.web.client: DEBUG
    org.apache.http: DEBUG

management:
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,configprops