package com.sleekflow.proxy.service;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public interface HttpForwardingService {
    Mono<ResponseEntity<byte[]>> forwardRequest(
            HttpMethod method,
            String path,
            HttpHeaders headers,
            byte[] body);

}
