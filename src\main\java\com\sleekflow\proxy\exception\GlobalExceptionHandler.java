package com.sleekflow.proxy.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.reactive.function.client.WebClientRequestException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(WebClientRequestException.class)
    public ResponseEntity<Map<String, Object>> handleWebClientRequestException(WebClientRequestException ex) {
        logger.error("Network error during request forwarding: {}", ex.getMessage(), ex);

        Map<String, Object> errorResponse = createErrorResponse(
                "NETWORK_ERROR",
                "Failed to connect to destination server",
                HttpStatus.BAD_GATEWAY.value()
        );

        Throwable rootCause = ex.getRootCause();
        if (rootCause instanceof ConnectException) {
            errorResponse.put("message", "Connection refused by destination server");
        } else if (rootCause instanceof SocketTimeoutException) {
            errorResponse.put("message", "Connection timeout to destination server");
        } else if (rootCause instanceof UnknownHostException) {
            errorResponse.put("message", "Unknown host: destination server not found");
        }

        return ResponseEntity.status(HttpStatus.BAD_GATEWAY).body(errorResponse);
    }

    @ExceptionHandler(WebClientResponseException.class)
    public ResponseEntity<Map<String, Object>> handleWebClientResponseException(WebClientResponseException ex) {
        logger.warn("HTTP error response from destination server: {} {}",
                ex.getStatusCode(), ex.getMessage());

        Map<String, Object> errorResponse = createErrorResponse(
                "DESTINATION_ERROR",
                "Destination server returned an error",
                ex.getStatusCode().value()
        );

        String responseBody = ex.getResponseBodyAsString();
        if (responseBody != null && !responseBody.isEmpty()) {
            errorResponse.put("destinationResponse", responseBody);
        }

        return ResponseEntity.status(ex.getStatusCode()).body(errorResponse);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException ex) {
        logger.error("Configuration error: {}", ex.getMessage(), ex);

        Map<String, Object> errorResponse = createErrorResponse(
                "CONFIGURATION_ERROR",
                "Invalid proxy configuration: " + ex.getMessage(),
                HttpStatus.INTERNAL_SERVER_ERROR.value()
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception ex) {
        logger.error("Unexpected error in proxy: {}", ex.getMessage(), ex);

        Map<String, Object> errorResponse = createErrorResponse(
                "INTERNAL_ERROR",
                "An unexpected error occurred in the proxy server",
                HttpStatus.INTERNAL_SERVER_ERROR.value()
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    private Map<String, Object> createErrorResponse(String errorCode, String message, int statusCode) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", errorCode);
        errorResponse.put("message", message);
        errorResponse.put("status", statusCode);
        errorResponse.put("timestamp", System.currentTimeMillis());
        return errorResponse;
    }
}
