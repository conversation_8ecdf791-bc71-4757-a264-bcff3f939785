package com.sleekflow.proxy.controller;

import com.sleekflow.proxy.config.ProxyProperties;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/_proxy")
public class HealthController {

    private final ProxyProperties proxyProperties;
    private final Instant startTime;

    public HealthController(ProxyProperties proxyProperties) {
        this.proxyProperties = proxyProperties;
        this.startTime = Instant.now();
    }

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", Instant.now());
        health.put("uptime", Instant.now().toEpochMilli() - startTime.toEpochMilli());
        return ResponseEntity.ok(health);
    }

    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> status() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("timestamp", Instant.now());
        status.put("startTime", startTime);
        status.put("uptime", Instant.now().toEpochMilli() - startTime.toEpochMilli());
        
        Map<String, Object> config = new HashMap<>();
        config.put("destinationHost", proxyProperties.getDestinationHost());
        config.put("serverPort", proxyProperties.getServerPort());
        
        status.put("configuration", config);
        return ResponseEntity.ok(status);
    }
}
